package com.example.kafkapractise;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest
@TestPropertySource(properties = {
    "spring.kafka.bootstrap-servers=localhost:9092"
})
class KafkaPractiseApplicationTests {

    @Test
    void contextLoads() {
        // This test will pass if the application context loads successfully
    }

}

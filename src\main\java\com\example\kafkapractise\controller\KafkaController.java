package com.example.kafkapractise.controller;

import com.example.kafkapractise.service.KafkaProducerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/kafka")
public class KafkaController {

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @PostMapping("/send")
    public ResponseEntity<String> sendMessage(@RequestParam String topic, 
                                            @RequestParam String message) {
        try {
            kafkaProducerService.sendMessage(topic, message);
            return ResponseEntity.ok("Message sent successfully to topic: " + topic);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to send message: " + e.getMessage());
        }
    }

    @PostMapping("/send-with-key")
    public ResponseEntity<String> sendMessageWithKey(@RequestParam String topic,
                                                    @RequestParam String key,
                                                    @RequestParam String message) {
        try {
            kafkaProducerService.sendMessageWithKey(topic, key, message);
            return ResponseEntity.ok("Message sent successfully to topic: " + topic + " with key: " + key);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to send message: " + e.getMessage());
        }
    }

    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("Kafka service is running!");
    }
}

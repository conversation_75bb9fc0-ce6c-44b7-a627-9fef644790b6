server:
  port: 8080

spring:
  application:
    name: kafka-practise
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      group-id: kafka-practise-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

logging:
  level:
    com.example.kafkapractise: DEBUG
    org.springframework.kafka: DEBUG

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics

# Kafka Practice - Spring Boot Application

This is a Spring Boot application for practicing Apache Kafka integration.

## Prerequisites

- Java 17 or higher
- Maven 3.6+
- Apache Kafka (running on localhost:9092)

## Project Structure

```
src/
├── main/
│   ├── java/com/example/kafkapractise/
│   │   ├── KafkaPractiseApplication.java     # Main application class
│   │   ├── config/
│   │   │   └── KafkaConfig.java              # Kafka configuration
│   │   ├── controller/
│   │   │   └── KafkaController.java          # REST endpoints for testing
│   │   └── service/
│   │       ├── KafkaProducerService.java     # Kafka producer service
│   │       └── KafkaConsumerService.java     # Kafka consumer service
│   └── resources/
│       └── application.yml                   # Application configuration
└── test/
    └── java/com/example/kafkapractise/
        └── KafkaPractiseApplicationTests.java
```

## Getting Started

### 1. Start Kafka

Make sure you have Kafka running on `localhost:9092`. If you don't have Kafka installed, you can use Docker:

```bash
# Start Zookeeper
docker run -d --name zookeeper -p 2181:2181 confluentinc/cp-zookeeper:latest

# Start Kafka
docker run -d --name kafka -p 9092:9092 \
  --link zookeeper:zookeeper \
  -e KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181 \
  -e KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092 \
  -e KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1 \
  confluentinc/cp-kafka:latest
```

### 2. Run the Application

```bash
mvn spring-boot:run
```

The application will start on `http://localhost:8080`

### 3. Test the Application

#### Send a message to a topic:
```bash
curl -X POST "http://localhost:8080/api/kafka/send?topic=practise-topic&message=Hello Kafka"
```

#### Send a message with a key:
```bash
curl -X POST "http://localhost:8080/api/kafka/send-with-key?topic=user-topic&key=user123&message=User data"
```

#### Check application health:
```bash
curl http://localhost:8080/api/kafka/health
```

## Features

- **Kafka Producer**: Send messages to Kafka topics
- **Kafka Consumer**: Consume messages from Kafka topics
- **Topic Configuration**: Auto-creation of topics with custom partitions
- **REST API**: HTTP endpoints for testing Kafka functionality
- **Logging**: Detailed logging for debugging
- **Health Checks**: Application health monitoring

## Configuration

The application is configured in `src/main/resources/application.yml`:

- Kafka bootstrap servers: `localhost:9092`
- Consumer group: `kafka-practise-group`
- Auto-created topics: `practise-topic`, `user-topic`

## Topics

The application automatically creates two topics:
- `practise-topic`: For general message practice (3 partitions)
- `user-topic`: For user-related messages (3 partitions)

## Testing

Run the tests with:
```bash
mvn test
```

## Next Steps

You can extend this application by:
- Adding more complex message types (JSON, Avro)
- Implementing error handling and retry mechanisms
- Adding message serialization/deserialization
- Creating custom partitioners
- Implementing exactly-once semantics
- Adding monitoring and metrics

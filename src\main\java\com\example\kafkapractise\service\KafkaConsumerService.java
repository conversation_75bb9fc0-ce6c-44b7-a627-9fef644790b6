package com.example.kafkapractise.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

@Service
public class KafkaConsumerService {

    private static final Logger logger = LoggerFactory.getLogger(KafkaConsumerService.class);

    @KafkaListener(topics = "practise-topic", groupId = "kafka-practise-group")
    public void consumePractiseMessage(@Payload String message,
                                       @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                       @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                       @Header(KafkaHeaders.OFFSET) long offset) {
        logger.info("Received message from topic: {}, partition: {}, offset: {}, message: {}", 
                   topic, partition, offset, message);
        
        // Process the message here
        processMessage(message);
    }

    @KafkaListener(topics = "user-topic", groupId = "kafka-practise-group")
    public void consumeUserMessage(@Payload String message,
                                   @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                   @Header(KafkaHeaders.RECEIVED_KEY) String key,
                                   @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                   @Header(KafkaHeaders.OFFSET) long offset) {
        logger.info("Received user message from topic: {}, key: {}, partition: {}, offset: {}, message: {}", 
                   topic, key, partition, offset, message);
        
        // Process the user message here
        processUserMessage(key, message);
    }

    private void processMessage(String message) {
        // Add your message processing logic here
        logger.info("Processing message: {}", message);
    }

    private void processUserMessage(String key, String message) {
        // Add your user message processing logic here
        logger.info("Processing user message with key {}: {}", key, message);
    }
}
